<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="flex items-center justify-between">
            <div>
                <h2 class="font-bold text-2xl text-gray-900">
                    My Bookings 📅
                </h2>
                <p class="text-gray-600 mt-1">Track and manage your kos bookings</p>
            </div>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="py-8">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <?php if(session('success')): ?>
                <div class="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg">
                    <?php echo e(session('success')); ?>

                </div>
            <?php endif; ?>

            <?php if($bookings->count() > 0): ?>
                <!-- Bookings List -->
                <div class="space-y-6">
                    <?php $__currentLoopData = $bookings; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $booking): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="card">
                            <div class="card-body">
                                <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
                                    <!-- Property Image -->
                                    <div class="lg:col-span-1">
                                        <div class="relative h-32 lg:h-24 bg-gray-200 rounded-lg overflow-hidden">
                                            <?php if($booking->kos->foto && count($booking->kos->foto) > 0): ?>
                                                <img src="<?php echo e(asset('storage/' . $booking->kos->foto[0])); ?>" 
                                                     alt="<?php echo e($booking->kos->nama_kos); ?>"
                                                     class="w-full h-full object-cover">
                                            <?php else: ?>
                                                <div class="w-full h-full flex items-center justify-center bg-gradient-to-br from-blue-100 to-purple-100">
                                                    <span class="text-2xl">🏠</span>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    
                                    <!-- Booking Details -->
                                    <div class="lg:col-span-2">
                                        <div class="flex items-start justify-between mb-2">
                                            <h3 class="font-bold text-lg text-gray-900"><?php echo e($booking->kos->nama_kos); ?></h3>
                                            <span class="px-3 py-1 rounded-full text-sm font-medium 
                                                <?php echo e($booking->status_pemesanan === 'confirmed' ? 'bg-green-100 text-green-800' : 
                                                   ($booking->status_pemesanan === 'pending' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800')); ?>">
                                                <?php echo e(ucfirst($booking->status_pemesanan)); ?>

                                            </span>
                                        </div>
                                        
                                        <p class="text-gray-600 text-sm mb-3 flex items-center">
                                            📍 <?php echo e($booking->kos->alamat); ?>

                                        </p>
                                        
                                        <div class="grid grid-cols-2 gap-4 text-sm">
                                            <div>
                                                <span class="text-gray-600">Check-in:</span>
                                                <p class="font-medium"><?php echo e($booking->tanggal_mulai->format('M d, Y')); ?></p>
                                            </div>
                                            <div>
                                                <span class="text-gray-600">Duration:</span>
                                                <p class="font-medium"><?php echo e($booking->durasi); ?> month<?php echo e($booking->durasi > 1 ? 's' : ''); ?></p>
                                            </div>
                                            <div>
                                                <span class="text-gray-600">Check-out:</span>
                                                <p class="font-medium"><?php echo e($booking->tanggal_mulai->copy()->addMonths((int) $booking->durasi)->format('M d, Y')); ?></p>
                                            </div>
                                            <div>
                                                <span class="text-gray-600">Total Price:</span>
                                                <p class="font-bold text-blue-600">Rp <?php echo e(number_format($booking->total_harga, 0, ',', '.')); ?></p>
                                            </div>
                                        </div>
                                        
                                        <div class="mt-3">
                                            <span class="text-gray-600 text-sm">Owner:</span>
                                            <div class="flex items-center space-x-2 mt-1">
                                                <div class="w-6 h-6 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white text-xs font-bold">
                                                    <?php echo e(substr($booking->kos->owner->name, 0, 1)); ?>

                                                </div>
                                                <span class="font-medium text-gray-900"><?php echo e($booking->kos->owner->name); ?></span>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Actions -->
                                    <div class="lg:col-span-1 flex flex-col space-y-2">
                                        <a href="<?php echo e(route('bookings.show', $booking)); ?>" 
                                           class="btn-primary text-center text-sm py-2">
                                            View Details
                                        </a>
                                        
                                        <?php if($booking->status_pemesanan === 'pending'): ?>
                                            <form action="<?php echo e(route('bookings.cancel', $booking)); ?>" 
                                                  method="POST" 
                                                  onsubmit="return confirm('Are you sure you want to cancel this booking?')">
                                                <?php echo csrf_field(); ?>
                                                <?php echo method_field('PATCH'); ?>
                                                <button type="submit" 
                                                        class="w-full btn-secondary text-sm py-2 text-red-600 hover:text-red-700 hover:bg-red-50">
                                                    Cancel Booking
                                                </button>
                                            </form>
                                        <?php endif; ?>
                                        
                                        <?php if($booking->status_pemesanan === 'confirmed' && !$booking->payment): ?>
                                            <a href="#" 
                                               class="btn-primary text-center text-sm py-2 bg-green-600 hover:bg-green-700">
                                                Pay Now
                                            </a>
                                        <?php endif; ?>
                                        
                                        <?php if($booking->payment && $booking->payment->status): ?>
                                            <div class="text-center">
                                                <span class="px-3 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                                                    ✓ Paid
                                                </span>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
                
                <!-- Pagination -->
                <div class="mt-8 flex justify-center">
                    <?php echo e($bookings->links()); ?>

                </div>
            <?php else: ?>
                <!-- Empty State -->
                <div class="card">
                    <div class="card-body text-center py-12">
                        <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                            <span class="text-gray-400 text-4xl">📅</span>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-2">No bookings yet</h3>
                        <p class="text-gray-600 mb-6">
                            You haven't made any bookings yet. Start by browsing available kos properties.
                        </p>
                        <a href="<?php echo e(route('kos.index')); ?>" class="btn-primary">
                            Browse Properties
                        </a>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\laragon\www\Rentalin\resources\views/bookings/index.blade.php ENDPATH**/ ?>