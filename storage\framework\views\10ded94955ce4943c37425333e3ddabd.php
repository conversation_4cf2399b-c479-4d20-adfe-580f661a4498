<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="flex items-center justify-between">
            <div>
                <h2 class="font-bold text-2xl text-gray-900">
                    My Properties 🏢
                </h2>
                <p class="text-gray-600 mt-1">Manage your kos properties</p>
            </div>
            <div class="hidden sm:flex items-center space-x-3 px-3">
                <a href="<?php echo e(route('owner.kos.create')); ?>" class="btn-primary">
                    + Add New Property
                </a>
            </div>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="py-8">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <?php if(session('success')): ?>
                <div class="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg">
                    <?php echo e(session('success')); ?>

                </div>
            <?php endif; ?>

            <?php if($kos->count() > 0): ?>
                <!-- Properties Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                    <?php $__currentLoopData = $kos; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $property): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="card group hover:shadow-2xl transition-all duration-300">
                            <!-- Image -->
                            <div class="relative h-48 bg-gray-200 overflow-hidden">
                                <?php if($property->foto && count($property->foto) > 0): ?>
                                    <img src="<?php echo e(asset('storage/' . $property->foto[0])); ?>" 
                                         alt="<?php echo e($property->nama_kos); ?>"
                                         class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300">
                                <?php else: ?>
                                    <div class="w-full h-full flex items-center justify-center bg-gradient-to-br from-blue-100 to-purple-100">
                                        <span class="text-4xl">🏠</span>
                                    </div>
                                <?php endif; ?>
                                
                                <!-- Status Badge -->
                                <div class="absolute top-4 right-4">
                                    <span class="px-3 py-1 rounded-full text-xs font-medium <?php echo e($property->status ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'); ?>">
                                        <?php echo e($property->status ? 'Available' : 'Unavailable'); ?>

                                    </span>
                                </div>
                            </div>
                            
                            <div class="card-body">
                                <!-- Title and Price -->
                                <div class="mb-3">
                                    <h3 class="font-bold text-lg text-gray-900 mb-1"><?php echo e($property->nama_kos); ?></h3>
                                    <p class="text-xl font-bold text-blue-600">
                                        Rp <?php echo e(number_format($property->harga, 0, ',', '.')); ?>

                                        <span class="text-sm text-gray-600 font-normal">/month</span>
                                    </p>
                                </div>
                                
                                <!-- Location -->
                                <p class="text-gray-600 text-sm mb-3 flex items-center">
                                    📍 <?php echo e(Str::limit($property->alamat, 50)); ?>

                                </p>

                                <!-- Owner Info (for admin) -->
                                <?php if(auth()->user()->email === '<EMAIL>' || auth()->user()->name === 'Admin User'): ?>
                                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-2 mb-3">
                                        <p class="text-xs text-blue-800 font-medium">
                                            👤 Owner: <?php echo e($property->owner->name ?? 'Unknown'); ?>

                                        </p>
                                    </div>
                                <?php endif; ?>
                                
                                <!-- Stats -->
                                <div class="grid grid-cols-2 gap-4 mb-4 text-center">
                                    <div class="bg-gray-50 rounded-lg p-3">
                                        <p class="text-lg font-bold text-gray-900"><?php echo e($property->bookings_count); ?></p>
                                        <p class="text-xs text-gray-600">Bookings</p>
                                    </div>
                                    <div class="bg-gray-50 rounded-lg p-3">
                                        <p class="text-lg font-bold text-gray-900"><?php echo e($property->reviews_count); ?></p>
                                        <p class="text-xs text-gray-600">Reviews</p>
                                    </div>
                                </div>
                                
                                <!-- Action Buttons -->
                                <div class="flex space-x-2">
                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view', $property)): ?>
                                        <a href="<?php echo e(route('owner.kos.show', $property)); ?>"
                                           class="flex-1 btn-secondary text-center text-sm py-2">
                                            View
                                        </a>
                                    <?php endif; ?>

                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('update', $property)): ?>
                                        <a href="<?php echo e(route('owner.kos.edit', $property)); ?>"
                                           class="flex-1 btn-primary text-center text-sm py-2">
                                            Edit
                                        </a>
                                    <?php endif; ?>

                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('delete', $property)): ?>
                                        <form action="<?php echo e(route('owner.kos.destroy', $property)); ?>"
                                              method="POST"
                                              class="inline"
                                              onsubmit="return confirm('Are you sure you want to delete this property?')">
                                            <?php echo csrf_field(); ?>
                                            <?php echo method_field('DELETE'); ?>
                                            <button type="submit"
                                                    class="px-3 py-2 bg-red-600 hover:bg-red-700 text-white text-sm rounded-lg transition-colors duration-200">
                                                🗑️
                                            </button>
                                        </form>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
                
                <!-- Pagination -->
                <div class="flex justify-center">
                    <?php echo e($kos->links()); ?>

                </div>
            <?php else: ?>
                <!-- Empty State -->
                <div class="card">
                    <div class="card-body text-center py-12">
                        <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                            <span class="text-gray-400 text-4xl">🏢</span>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-2">No properties yet</h3>
                        <p class="text-gray-600 mb-6">
                            Start by adding your first kos property to begin earning rental income.
                        </p>
                        <a href="<?php echo e(route('owner.kos.create')); ?>" class="btn-primary">
                            + Add Your First Property
                        </a>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\laragon\www\Rentalin\resources\views/owner/kos/index.blade.php ENDPATH**/ ?>